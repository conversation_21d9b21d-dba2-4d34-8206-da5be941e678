import Vue from 'vue'
import VueCountUp from 'vue-countupjs'
import layer from 'vue-pubilc-layer'
import elementUi from 'element-ui'
import App from './App.vue'
import { initRouter } from './router'
import 'element-ui/lib/theme-chalk/index.css'
import './index.less'
import vueSeamlessScroll from 'vue-seamless-scroll' // 循环滚动
import * as echarts from "echarts";
import mixin from '@/minxins/setTimerMinxin'
import { numberFilter } from '@/utils/index'
import moment from 'moment'
import AmapVue from "@amap/amap-vue";
import '@/utils/echarts-wordCloud.js'
// 导入 Treeselect 组件
import Treeselect from '@riophae/vue-treeselect/src'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
AmapVue.config.key = "df956d6b0e5f77e7e41398cdaf541e5d";
Vue.use(AmapVue);
Vue.prototype.$moment = moment
Vue.use(vueSeamlessScroll)
// 全局注册 Treeselect 组件
Vue.component('Treeselect', Treeselect)
const router = initRouter()
Vue.use(elementUi)
// Vue.use(Image)
Vue.mixin(mixin)
Vue.filter('numberFilter', numberFilter)
Vue.config.productionTip = false
Vue.prototype.$pageWidth = 1920
Vue.prototype.$pageHeight = 1080
Vue.prototype.$echarts = echarts;
Vue.prototype.$bus = new Vue();
// Vue.prototype.timeMixin = mixin;

router.beforeEach((to, from, next) => {
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

new Vue({
  router,
  render: (h) => h(App),
}).$mount('#app')
