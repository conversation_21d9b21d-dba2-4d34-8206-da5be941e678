<template>
  <div>
    <div class="">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item prop="yyId">
          <el-select v-model="queryParams.yyId" placeholder="所属应用" clearable style="width: 160px">
            <el-option v-for="(item, i) in appOptions" :key="i" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item prop="deptId">
          <treeselect
            style="width: 160px; height: 32px"
            v-model="queryParams.deptId"
            :options="enabledDeptOptions"
            :show-count="true"
            placeholder="请选择归属部门"
          />
        </el-form-item>
        <el-form-item prop="level">
          <el-select v-model="queryParams.level" placeholder="告警等级" clearable>
            <el-option v-for="dict in levelOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="dateArr">
          <el-date-picker
            v-model="queryParams.dateArr"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <CommonTable :height="'500px'" :tableData="tableData"></CommonTable>
    </div>
    <CommonPagination :total="total" @pagination="changePage"></CommonPagination>
  </div>
</template>

<script>
import CommonTable from '@/components/CommonTable/index'
import CommonPagination from '@/components/CommonPagination/index'
import { listAllYy, deptTreeSelect } from "@/api/common";

export default {
  components: { CommonTable, CommonPagination },
  props: {},
  watch: {},
  data() {
    return {
      params: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 100,
      queryParams: {
        yyId: undefined,
        deptId: undefined,
        level: undefined,
        dateArr: [],
      },
      appOptions: [],
      enabledDeptOptions: [],
      levelOptions: [
        { label: '特别紧急', value: '1' },
        { label: '紧急', value: '2' },
        { label: '重要', value: '3' },
        { label: '一般', value: '4' },
      ],
      tableData: {
        thead: [
          { label: '县市区', property: 'area', width: 100, align: 'left' },
          { label: '得分', property: 'score', width: 120, align: 'left' },
          { label: '排名', property: 'ranking', width: 80, align: 'left' },
        ],
        tbody: [],
      },
    }
  },
  methods: {
    initData() {
      listAllYy().then((res) => {
        this.appOptions = res.data
      })
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
      })
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children)
        }
        return true
      })
    },
    changePage(obj) {
      this.params.pageNum = obj.page
      this.getList()
    },
  },
}
</script>

<style lang="less" scoped>
.table {
  padding: 0 30px;
  margin-top: 20px;
}
.footer {
  margin: 30px;
}
</style>